# Rental Apartments Scraper

This extension adds rental apartment scraping functionality to the existing Otodom Real Estate Data Scraper. The system now supports both **sale** and **rental** apartment listings with separate databases and processing pipelines.

## 🏠 Features

### Rental-Specific Features
- **Separate Database**: Dedicated tables for rental listings, price history, photos, and features
- **Rental-Specific Fields**: Deposit amount, utilities included, additional rent
- **Price Tracking**: Monitor rental price changes over time
- **Closed Listings**: Track when rental offers are removed or rented

### Shared Features
- **Location Management**: Reuses existing location data (voivodeship, city, district)
- **Robust Scraping**: Rate limiting, error handling, robots.txt compliance
- **Data Validation**: Comprehensive data validation and transformation
- **Logging**: Detailed logging with configurable levels

## 📊 Database Schema

### New Tables for Rentals

#### `apartments_rental_listings`
Main table for rental apartment listings with fields like:
- `rental_price` - Monthly rental price
- `deposit_amount` - Security deposit
- `utilities_included` - Whether utilities are included
- `additional_rent` - Additional administrative fees

#### `rental_price_history`
Tracks rental price changes over time

#### `rental_photos` & `rental_features`
Separate tables for rental listing photos and features

## 🚀 Quick Start

### 1. Setup Environment
Ensure you have the same environment as the main scraper:
```bash
# Install dependencies (if not already done)
pip install -r requirements.txt

# Set up environment variables in .env file
DB_HOST=localhost
DB_NAME=apartments_for_sale  # Same database, new tables
DB_USER=your_username
DB_PASSWORD=your_password
DB_PORT=5432
```

### 2. Run Rental Scraper
```bash
# Run rental scraper for Warsaw
python main_rental.py

# Or test with limited offers
python test_rental_scraper.py
```

### 3. Custom City Configuration
```python
from main_rental import main_with_custom_city

# Scrape rentals in Krakow
main_with_custom_city("kraków", "małopolskie")

# Scrape with limited offers for testing
main_with_custom_city("gdańsk", "pomorskie", limit_offers=10)
```

## 📁 File Structure

```
├── main_rental.py                 # Main rental scraping application
├── test_rental_scraper.py         # Test suite for rental functionality
├── db/
│   ├── rental_db_operations.py    # Database operations for rentals
│   └── schema.sql                 # Updated schema with rental tables
├── scraper/
│   ├── rental_scraper.py          # Main rental scraping logic
│   ├── rental_fetch_and_parse.py  # Rental data fetching and parsing
│   └── rental_transform_data.py   # Rental data transformation
└── README_RENTAL.md               # This file
```

## 🔧 Configuration

### URL Construction
The system automatically constructs rental search URLs:
```python
from scraper.rental_scraper import get_rental_search_url

# Generate URL for Warsaw rentals
url = get_rental_search_url("warszawa", "mazowieckie")
# Result: https://www.otodom.pl/pl/wyniki/wynajem/mieszkanie/mazowieckie/warszawa/...
```

### Search Parameters
- `city`: Target city (required)
- `voivodeship`: Voivodeship/province (optional)
- `district`: District within city (optional)
- `sort_by`: Sorting method (default: "LATEST")
- `direction`: Sort direction (default: "DESC")
- `limit`: Results per page (default: 72)

## 🧪 Testing

### Run Test Suite
```bash
python test_rental_scraper.py
```

The test suite validates:
- ✅ URL generation for different cities
- ✅ Robots.txt compliance
- ✅ Basic page fetching
- ✅ Search results parsing
- ✅ Detailed offer parsing
- ✅ Data transformation and validation

### Manual Testing
```python
# Test specific functionality
from scraper.rental_scraper import validate_rental_offer_data
from scraper.rental_transform_data import transform_rental_data

# Validate offer data
is_valid = validate_rental_offer_data(offer_data)

# Transform raw data
transformed = transform_rental_data(raw_data)
```

## 📈 Usage Examples

### Basic Rental Scraping
```python
import logging
from main_rental import main

# Setup logging
logging.basicConfig(level=logging.INFO)

# Run scraper
main()
```

### Advanced Usage
```python
from scraper.rental_scraper import scrape_rental_offer
from scraper.rental_fetch_and_parse import download_rental_data_from_search_results

# Get rental offers from search results
url = "https://www.otodom.pl/pl/wyniki/wynajem/mieszkanie/mazowieckie/warszawa/..."
offers = download_rental_data_from_search_results(url)

# Scrape detailed data for each offer
for offer in offers[:5]:  # Limit for testing
    detailed_data = scrape_rental_offer(offer)
    if detailed_data:
        print(f"Rental: {detailed_data['rental_price']} PLN/month")
```

## 🔍 Data Fields

### Rental-Specific Fields
- `rental_price`: Monthly rental price (PLN)
- `rental_price_per_m`: Price per square meter (PLN/m²)
- `deposit_amount`: Security deposit (PLN)
- `utilities_included`: Whether utilities are included (boolean)
- `additional_rent`: Additional administrative fees (PLN)

### Shared Fields
All standard fields from the sale listings:
- `title`, `description_text`, `area`, `rooms_num`
- `location` data (voivodeship, city, district, street)
- `building` data (year, floors, material, type)
- `features` (furniture, appliances, amenities)
- `owner` and `agency` information

## 🚨 Important Notes

### Database Considerations
- Uses the same PostgreSQL database as sale listings
- Rental tables are separate from sale tables
- Shared `locations` table for geographical data
- Ensure database has sufficient space for both datasets

### Rate Limiting
- Built-in delays between requests (1-2 seconds)
- Respects robots.txt guidelines
- Handles HTTP errors gracefully

### Data Quality
- Validates all scraped data before database insertion
- Handles missing or malformed data gracefully
- Logs warnings for data quality issues

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   ```
   Solution: Check .env file and database credentials
   ```

2. **No Rental Offers Found**
   ```
   Solution: Verify URL format and city names
   ```

3. **Parsing Errors**
   ```
   Solution: Check if Otodom changed their HTML structure
   ```

### Debug Mode
Enable debug logging for detailed information:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📝 Logging

The system provides comprehensive logging:
- **INFO**: General progress and statistics
- **DEBUG**: Detailed scraping information
- **WARNING**: Data quality issues
- **ERROR**: Scraping failures
- **CRITICAL**: System failures

Logs are saved to `logs/` directory with timestamps.

## 🔄 Integration with Existing System

The rental scraper is designed to work alongside the existing sale scraper:
- **Shared**: Database connection, logging, utilities
- **Separate**: Tables, processing logic, main applications
- **Compatible**: Can run both scrapers simultaneously

## 📊 Performance

### Typical Performance
- **Search Results**: ~2-3 seconds per page
- **Detailed Offers**: ~1-2 seconds per offer
- **Database Operations**: ~100ms per insert/update
- **Memory Usage**: ~50-100MB for typical session

### Optimization Tips
- Use `limit_offers` parameter for testing
- Run during off-peak hours
- Monitor database performance
- Consider parallel processing for large datasets

---

For questions or issues, refer to the main project documentation or create an issue in the repository.
