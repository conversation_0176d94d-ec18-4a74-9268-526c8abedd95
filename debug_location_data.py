#!/usr/bin/env python3
"""
Debug script to check location data extraction from rental listings.
"""

import os
import sys
import json
import logging

# Add scraper path
scraper_path = os.path.join(os.path.dirname(__file__), 'scraper')
if scraper_path not in sys.path:
    sys.path.append(scraper_path)

from scraper.rental_fetch_and_parse import fetch_page
from bs4 import BeautifulSoup

# Setup logging
logging.basicConfig(level=logging.DEBUG)

def debug_location_extraction(url):
    """Debug location data extraction from a rental listing"""
    print(f"Debugging location extraction for: {url}")
    
    # Fetch the page
    response = fetch_page(url)
    if not response:
        print("Failed to fetch page")
        return
    
    # Parse HTML
    soup = BeautifulSoup(response.text, 'html.parser')
    script_tag = soup.find('script', {'id': '__NEXT_DATA__'})
    
    if not script_tag:
        print("No __NEXT_DATA__ script found")
        return
    
    try:
        json_data = json.loads(script_tag.get_text())
        offer_data = json_data.get("props", {}).get("pageProps", {}).get("ad", {})
        
        if not offer_data:
            print("No offer data found")
            return
        
        print("\n" + "="*60)
        print("FULL OFFER DATA STRUCTURE")
        print("="*60)
        
        # Save full data for inspection
        with open('debug_offer_data.json', 'w', encoding='utf-8') as f:
            json.dump(offer_data, f, indent=2, ensure_ascii=False, default=str)
        print("Full offer data saved to: debug_offer_data.json")
        
        # Check location data specifically
        print("\n" + "="*60)
        print("LOCATION DATA ANALYSIS")
        print("="*60)
        
        location = offer_data.get("location", {})
        print(f"Location object: {json.dumps(location, indent=2, ensure_ascii=False)}")
        
        # Check different possible location fields
        possible_location_fields = [
            "location", "address", "geo", "place", "region", "area", "locality"
        ]
        
        for field in possible_location_fields:
            if field in offer_data:
                print(f"\n{field}: {json.dumps(offer_data[field], indent=2, ensure_ascii=False)}")
        
        # Check if location data is nested elsewhere
        print("\n" + "="*60)
        print("SEARCHING FOR LOCATION KEYWORDS")
        print("="*60)
        
        def search_for_keywords(data, keywords, path=""):
            """Recursively search for keywords in JSON data"""
            if isinstance(data, dict):
                for key, value in data.items():
                    current_path = f"{path}.{key}" if path else key
                    if any(keyword.lower() in key.lower() for keyword in keywords):
                        print(f"Found '{key}' at {current_path}: {value}")
                    if isinstance(value, (dict, list)):
                        search_for_keywords(value, keywords, current_path)
            elif isinstance(data, list):
                for i, item in enumerate(data):
                    current_path = f"{path}[{i}]"
                    if isinstance(item, (dict, list)):
                        search_for_keywords(item, keywords, current_path)
        
        location_keywords = ["city", "province", "voivodeship", "district", "street", "warszawa", "mazowieckie"]
        search_for_keywords(offer_data, location_keywords)
        
        # Check breadcrumbs or navigation data
        print("\n" + "="*60)
        print("CHECKING BREADCRUMBS/NAVIGATION")
        print("="*60)
        
        breadcrumb_fields = ["breadcrumbs", "navigation", "path", "url", "slug"]
        for field in breadcrumb_fields:
            if field in offer_data:
                print(f"{field}: {offer_data[field]}")
        
        # Check the URL itself for location info
        print(f"\nURL: {url}")
        if "warszawa" in url.lower():
            print("URL contains 'warszawa' - this is likely a Warsaw listing")
        
    except Exception as e:
        print(f"Error parsing JSON: {e}")

def main():
    """Main debug function"""
    # Test URLs from the logs
    test_urls = [
        "https://www.otodom.pl/pl/oferta/nowe-3-pok-oddzielne-metro-ursynow-balkon-ID4vJtH",
        "https://www.otodom.pl/pl/oferta/wyjatkowy-4-pokojowy-apartament-w-sercu-warszawy-ID4vJtm"
    ]
    
    for url in test_urls:
        debug_location_extraction(url)
        print("\n" + "="*80 + "\n")

if __name__ == "__main__":
    main()
