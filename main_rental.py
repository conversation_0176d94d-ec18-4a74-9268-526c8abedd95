import os, sys, logging
from colorlog import ColoredFormatter
scraper_path = os.path.join(os.path.dirname(__file__), 'scraper')
if scraper_path not in sys.path:
    sys.path.append(scraper_path)

from datetime import datetime
from scraper.rental_scraper import is_allowed_to_scrape, scrape_rental_offer, get_rental_search_url, log_rental_scraping_summary
from scraper.rental_fetch_and_parse import download_rental_data_from_search_results, check_if_rental_offer_exists, check_if_rental_price_changed, find_closed_rental_offers
from db.db_setup import create_tables
from db.rental_db_operations import insert_new_rental_listing, update_active_rental_offers, update_deleted_rental_offers
from db.db_setup import get_db_connection

from config.logging_config import setup_logger
logger = setup_logger()

# ZASADY: WYSZUKIWANIE MIESZKAN NA WYNAJEM W DANYM MIESCIE BEZ ZADNYCH FILTROW, ZALECANE SORTOWANIE OD NAJNOWSZYCH I MAX LIMIT OFERT NA STRONE

# Przykładowe URL dla wynajmu mieszkań w różnych miastach
# url = "https://www.otodom.pl/pl/wyniki/wynajem/mieszkanie/mazowieckie/warszawa/warszawa/warszawa?viewType=listing&by=LATEST&direction=DESC&limit=72"
# url = "https://www.otodom.pl/pl/wyniki/wynajem/mieszkanie/slaskie/katowice/katowice/katowice?viewType=listing&by=LATEST&direction=DESC&limit=72"

# Konfiguracja dla wynajmu
city = 'warszawa'
voivodeship = 'mazowieckie'
url = get_rental_search_url(city, voivodeship)

def main():
    conn = None
    cur = None
    
    # Liczniki dla podsumowania
    total_offers_processed = 0
    new_offers_added = 0
    updated_offers_count = 0
    closed_offers_count = 0
    
    try:
        conn = get_db_connection()
        if conn is None:
            logging.critical("Connection to the database failed")
            return
        cur = conn.cursor()
        
        # Upewnij się, że to dozwolone
        result = is_allowed_to_scrape(url)
        logging.warning(f"Is fetching rental page {url} allowed?: {result}")

        # Utwórz tabele jeżeli nie istnieją
        create_tables(cur)

        # Pobierz dane o ofertach wynajmu
        logging.info("Rozpoczynam pobieranie podstawowych danych z wyniku wyszukiwania ofert wynajmu...")
        all_rental_offers_basic = download_rental_data_from_search_results(url)
        
        # Ograniczenie dla testów
        all_rental_offers_basic = all_rental_offers_basic[:5]
        logging.info(f"Temporarily limiting processing to the first {len(all_rental_offers_basic)} rental offers for testing.")

        logging.debug(f"Dane z all_rental_offers_basic: \n{'--' * 100}\n{all_rental_offers_basic}\n {'--' * 100}\n")

        total_offers_processed = len(all_rental_offers_basic)

        # Sprawdź które oferty już są w bazie (i dodaj/aktualizuj cenę): 
        logging.info("\nRozpoczynam sprawdzanie i pobieranie ofert wynajmu...")
        for offer in all_rental_offers_basic:
            id = offer.get("listing_id")
            if len(str(id)) != 8: 
                continue
            logging.debug(f"Sprawdzam ofertę wynajmu {id}")
            
            # Jeżeli dana oferta nie znajduje się jeszcze w bazie, pobierz ją i zapisz
            if not check_if_rental_offer_exists(offer, cur):
                offer_data = scrape_rental_offer(offer)  # pobierz znalezioną ofertę w całości
                if offer_data:  # jeżeli brak błędu wstaw dane do bazy
                    id_db = insert_new_rental_listing(offer_data, conn, cur)  # wstaw do bazy
                    if id_db:
                        new_offers_added += 1
                        logging.info(f"Oferta wynajmu {id} zapisana w bazie pod id {id_db}\n")
                    else:
                        logging.warning(f"Nie udało się zapisać oferty wynajmu {id} w bazie.")
                else:
                    logging.warning(f"Nie udało się pobrać pełnych danych oferty wynajmu {id} – pomijam.")

            # jeżeli oferta się znajduje, sprawdź czy nie zmieniła się cena
            else:
                logging.info(f"Oferta wynajmu {id} istnieje w bazie, sprawdzanie czy zmieniła się cena...")
                price_check_result = check_if_rental_price_changed(offer, cur)
                if price_check_result is not None:
                    id_db, new_price, new_price_per_m = price_check_result
                    # Jeżeli new_price to nie False tylko liczba tzn że cena się zmieniła - update bazy
                    if new_price is not False and new_price is not None:  # Indicates a price change was found
                        update_active_rental_offers((id_db, new_price, new_price_per_m), conn, cur)
                        updated_offers_count += 1
                        logging.info(f"Update ceny oferty wynajmu {id} ({id_db}) w bazie zakończony. Nowa cena: {new_price}, nowa cena za m2: {new_price_per_m}")
                    elif new_price is False:
                        logging.info(f"Cena oferty wynajmu {id} ({id_db}) nie zmieniła się.")
                    else:  # new_price is None, but price_check_result was not None
                        logging.warning(f"check_if_rental_price_changed zwrócił nieoczekiwany wynik dla oferty wynajmu {id} ({id_db}): {price_check_result}")
                else:
                    logging.error(f"Nie udało się sprawdzić zmiany ceny dla oferty wynajmu {id} (check_if_rental_price_changed zwrócił None).")

        # Na końcu sprawdź, czy są jakieś usunięte oferty wynajmu
        logging.info("Rozpoczynam sprawdzanie czy jakieś oferty wynajmu nie zostały usunięte z otodom...")
        deleted_rental_offers = find_closed_rental_offers(all_rental_offers_basic, city, cur)
        closed_offers_count = len(deleted_rental_offers)
        
        logging.info("Rozpoczynam update ofert wynajmu w bazie, które zostały usunięte...")
        for deleted_offer in deleted_rental_offers:
            update_deleted_rental_offers(deleted_offer, conn, cur)
        
        # Wyświetl podsumowanie
        log_rental_scraping_summary(total_offers_processed, new_offers_added, updated_offers_count, closed_offers_count)
        
        logging.info("Zakończono scraping ofert wynajmu")
            
    except Exception as error:
        logging.exception("Error in main rental function:")
    finally: 
        if conn:
            if cur:  # Ensure cur is not None before closing
                cur.close()
            conn.close()


def main_with_custom_city(city_name: str, voivodeship_name: str | None = None, limit_offers: int | None = None):
    """
    Main function with custom city configuration for rental scraping.
    
    Args:
        city_name (str): Name of the city to scrape
        voivodeship_name (str, optional): Name of the voivodeship
        limit_offers (int, optional): Limit number of offers to process (for testing)
    """
    global city, voivodeship, url
    
    city = city_name.lower()
    voivodeship = voivodeship_name.lower() if voivodeship_name else None
    url = get_rental_search_url(city, voivodeship)
    
    logging.info(f"Rozpoczynam scraping ofert wynajmu dla miasta: {city_name}")
    logging.info(f"URL wyszukiwania: {url}")
    
    if limit_offers:
        logging.info(f"Ograniczenie liczby ofert do: {limit_offers}")
    
    main()


def test_rental_scraping():
    """
    Test function for rental scraping with minimal data.
    """
    logging.info("Rozpoczynam test scrapingu ofert wynajmu...")
    
    # Test z ograniczoną liczbą ofert
    main_with_custom_city("warszawa", "mazowieckie", limit_offers=3)
    
    logging.info("Test scrapingu ofert wynajmu zakończony.")


if __name__ == "__main__": 
    # Możesz uruchomić różne tryby:
    
    # 1. Standardowy scraping dla Warszawy
    main()
    
    # 2. Scraping dla innego miasta
    # main_with_custom_city("kraków", "małopolskie")
    
    # 3. Test z ograniczoną liczbą ofert
    # test_rental_scraping()
