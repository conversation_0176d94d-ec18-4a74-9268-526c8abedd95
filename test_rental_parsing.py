#!/usr/bin/env python3
"""
Test script to validate rental data parsing without database operations.
This helps debug parsing issues independently of database problems.
"""

import os
import sys
import logging
import json

# Add scraper path
scraper_path = os.path.join(os.path.dirname(__file__), 'scraper')
if scraper_path not in sys.path:
    sys.path.append(scraper_path)

from scraper.rental_fetch_and_parse import (
    fetch_page, 
    download_rental_data_from_search_results,
    download_rental_data_from_listing_page
)
from scraper.rental_transform_data import transform_rental_data
from scraper.rental_scraper import get_rental_search_url

# Setup logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_search_results_parsing():
    """Test parsing of rental search results"""
    print("\n" + "="*60)
    print("TESTING RENTAL SEARCH RESULTS PARSING")
    print("="*60)
    
    # Get URL for Warsaw rentals
    url = get_rental_search_url("warszawa", "mazowieckie")
    print(f"Testing URL: {url}")
    
    try:
        offers = download_rental_data_from_search_results(url)
        
        if offers:
            print(f"✓ Successfully parsed {len(offers)} rental offers")
            
            # Show first few offers
            for i, offer in enumerate(offers[:3]):
                print(f"\nOffer {i+1}:")
                print(f"  ID: {offer.get('listing_id')}")
                print(f"  Area: {offer.get('area')} m²")
                print(f"  Rental Price: {offer.get('rental_price')} PLN")
                print(f"  Price per m²: {offer.get('rental_price_per_m')} PLN/m²")
                print(f"  Link: {offer.get('link')}")
            
            return offers[:2]  # Return first 2 offers for detailed testing
        else:
            print("✗ No rental offers found")
            return []
            
    except Exception as e:
        print(f"✗ Error parsing search results: {e}")
        logging.exception("Detailed error:")
        return []


def test_detailed_parsing(offers):
    """Test parsing of detailed rental offer pages"""
    print("\n" + "="*60)
    print("TESTING DETAILED RENTAL OFFER PARSING")
    print("="*60)
    
    if not offers:
        print("No offers to test detailed parsing")
        return []
    
    detailed_offers = []
    
    for i, offer in enumerate(offers):
        print(f"\nTesting offer {i+1}: {offer.get('listing_id')}")
        
        try:
            link = offer.get('link')
            if not link:
                print("✗ No link available")
                continue
                
            print(f"Fetching: {link}")
            response = fetch_page(link)
            if not response:
                print("✗ Failed to fetch offer page")
                continue
                
            print("Parsing detailed data...")
            detailed_data = download_rental_data_from_listing_page(response)
            
            if detailed_data:
                print(f"✓ Successfully parsed detailed data")
                
                # Show key fields
                print(f"  Title: {detailed_data.get('title', 'N/A')[:50]}...")
                print(f"  Rental Price: {detailed_data.get('rental_price')} PLN")
                print(f"  Area: {detailed_data.get('area')} m²")
                print(f"  Rooms: {detailed_data.get('rooms_num')}")
                print(f"  Floor: {detailed_data.get('floor_num')}")
                print(f"  Deposit: {detailed_data.get('deposit_amount')} PLN")
                print(f"  Utilities Included: {detailed_data.get('utilities_included')}")
                print(f"  Location: {detailed_data.get('city')}, {detailed_data.get('district')}")
                print(f"  Street: {detailed_data.get('street')}")
                
                # Save raw data for debugging
                filename = f"rental_offer_{detailed_data.get('listing_id', 'unknown')}.json"
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(detailed_data, f, indent=2, ensure_ascii=False, default=str)
                print(f"  Raw data saved to: {filename}")
                
                detailed_offers.append(detailed_data)
            else:
                print("✗ Failed to parse detailed data")
                
        except Exception as e:
            print(f"✗ Error parsing detailed offer: {e}")
            logging.exception("Detailed error:")
    
    return detailed_offers


def test_data_transformation(detailed_offers):
    """Test data transformation"""
    print("\n" + "="*60)
    print("TESTING DATA TRANSFORMATION")
    print("="*60)
    
    if not detailed_offers:
        print("No detailed offers to test transformation")
        return []
    
    transformed_offers = []
    
    for i, offer in enumerate(detailed_offers):
        print(f"\nTransforming offer {i+1}: {offer.get('listing_id')}")
        
        try:
            transformed = transform_rental_data(offer)
            
            if transformed:
                print("✓ Successfully transformed data")
                
                # Show key transformed fields
                print(f"  Listing ID: {transformed.get('listing_id')}")
                print(f"  Rental Price: {transformed.get('rental_price')} PLN")
                print(f"  Area: {transformed.get('area')} m²")
                print(f"  Rooms: {transformed.get('rooms_num')}")
                print(f"  Floor: {transformed.get('floor_num')}")
                print(f"  Creation Date: {transformed.get('creation_date')}")
                print(f"  Creation Time: {transformed.get('creation_time')}")
                print(f"  Active: {transformed.get('active')}")
                
                # Save transformed data
                filename = f"rental_transformed_{transformed.get('listing_id', 'unknown')}.json"
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(transformed, f, indent=2, ensure_ascii=False, default=str)
                print(f"  Transformed data saved to: {filename}")
                
                transformed_offers.append(transformed)
            else:
                print("✗ Failed to transform data")
                
        except Exception as e:
            print(f"✗ Error transforming data: {e}")
            logging.exception("Detailed error:")
    
    return transformed_offers


def main():
    """Main test function"""
    print("RENTAL DATA PARSING TEST")
    print("=" * 80)
    
    # Test 1: Search results parsing
    offers = test_search_results_parsing()
    
    if not offers:
        print("\n✗ Cannot continue - no offers found in search results")
        return False
    
    # Test 2: Detailed offer parsing
    detailed_offers = test_detailed_parsing(offers)
    
    if not detailed_offers:
        print("\n✗ Cannot continue - no detailed offers parsed")
        return False
    
    # Test 3: Data transformation
    transformed_offers = test_data_transformation(detailed_offers)
    
    # Summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    print(f"Search Results: {'✓ PASSED' if offers else '✗ FAILED'} ({len(offers)} offers)")
    print(f"Detailed Parsing: {'✓ PASSED' if detailed_offers else '✗ FAILED'} ({len(detailed_offers)} detailed)")
    print(f"Data Transformation: {'✓ PASSED' if transformed_offers else '✗ FAILED'} ({len(transformed_offers)} transformed)")
    
    success = bool(offers and detailed_offers and transformed_offers)
    print(f"\nOVERALL: {'✓ ALL TESTS PASSED' if success else '✗ SOME TESTS FAILED'}")
    
    if success:
        print("\n✓ Rental parsing is working correctly!")
        print("You can now run the full scraper with database operations.")
    else:
        print("\n✗ Some issues found in rental parsing.")
        print("Check the logs and saved JSON files for debugging.")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
