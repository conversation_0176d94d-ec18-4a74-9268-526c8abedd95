import os,sys, logging
from colorlog import ColoredFormatter
scraper_path = os.path.join(os.path.dirname(__file__), 'scraper')
if scraper_path not in sys.path:
    sys.path.append(scraper_path)

from datetime import datetime
from scraper.scraper import is_allowed_to_scrape, scrape_offer
from scraper.fetch_and_parse import download_data_from_search_results, check_if_offer_exists, check_if_price_changed, find_closed_offers
from db.db_setup import create_tables
from db.db_operations import insert_new_listing, update_active_offers, update_deleted_offers, get_db_connection

from config.logging_config import setup_logger
logger = setup_logger()

# ZASADY: WYSZUKIWANIE MIESZKAN NA SPRZEDAZ W DANYM MIESCIE BEZ ZADNYCH FILTROW, ZALECANE SORTOWANIE OD NAJNOWSZYCH I MAX LIMIT OFERT NA STRONE

#url_main = "https://www.otodom.pl/pl/wyniki/sprzedaz/mieszkanie/slaskie/katowice?by=LATEST&direction=DESC"
url= "https://www.otodom.pl/pl/wyniki/sprzedaz/mieszkanie/mazowieckie/warszawa/warszawa/warszawa?viewType=listing&by=LATEST&direction=DESC&limit=72"
#url= "https://www.otodom.pl/pl/wyniki/sprzedaz/mieszkanie/slaskie/katowice/katowice/katowice?viewType=listing&by=LATEST&direction=DESC"
city = 'warszawa'

def main():
    conn = None
    cur = None  
    try:
        
        conn = get_db_connection()
        if conn is None:
            logging.critical("Connection to the database failed")
            return
        cur=conn.cursor()
        
        # Upewnij się, ze to dozwolone
        result = is_allowed_to_scrape(url)
        logging.warning(f"Is fetching page {url} allowed?: {result}")

        # Utwórz tabele jezeli nie istnieją
        create_tables(cur)

        # pobierz dane
        logging.info("Rozpoczynam pobieranie podstawowych danych z wyniku wyszukiwania...")
        all_offers_basic_from_sarching_page = download_data_from_search_results(url)
        all_offers_basic_from_sarching_page = all_offers_basic_from_sarching_page[:5]
        logging.info(f"Temporarily limiting processing to the first {len(all_offers_basic_from_sarching_page)} offers for testing.")

        logging.debug(f"Dane z all_offers_basic_from_sarching_page: \n{'--' * 100}\n{all_offers_basic_from_sarching_page}\n {'--' * 100}\n")

        # sprawdz ktore oferty juz sa w bazie (i dodaj/aktualizuj cene): 
        logging.info("\nRozpoczynam sprawdzanie i pobieranie ofert...")
        for offer in all_offers_basic_from_sarching_page:
            id = offer.get("listing_id")
            if len(str(id)) !=8: 
                continue
            logging.debug(f"Sprawdzam oferte {id}")
            # Jezeli dana oferta nie znajduje sie jeszcze w bazie, pobierz ja i zapisz
            if not check_if_offer_exists(offer, cur):
                offer_data = scrape_offer(offer) # pobierz znaleziona oferte w całości
                if offer_data: # jezeli brak bledu wstaw dane do bazy
                    id_db = insert_new_listing(offer_data, conn, cur) # wstaw do bazy
                    logging.info(f"Oferta {id} zapisana w bazie pod id {id_db}\n")
                else:
                    logging.warning(f"Nie udało się pobrać pełnych danych oferty {id} – pomijam.")

            # jezeli oferta sie znajduje, sprawdz czy nie zmienila sie cena
            else:
                logging.info(f"Oferta {id} istnieje w bazie, sprawdzanie czy zmieniła się cena...")
                price_check_result = check_if_price_changed(offer, cur)
                if price_check_result is not None:
                    id_db, new_price, new_price_per_m = price_check_result
                    # Jezeli new_price to nie False tylko liczba tzn ze cena sie zmienila - update bazy
                    # new_price can be False if price hasn't changed, or a float/None if it has or if there was an issue.
                    # The original logic `if new_price:` would be true for a numeric price and false for False.
                    # We should explicitly check if new_price is a number (float) or not False, depending on intended logic.
                    # Given the previous return `(id_db, False, False)`, `if new_price:` was meant to catch actual price changes.
                    if new_price is not False and new_price is not None: # Indicates a price change was found
                        update_active_offers((id_db, new_price, new_price_per_m), conn, cur)
                        logging.info(f"Update ceny oferty {id} ({id_db}) w bazie zakończony. Nowa cena: {new_price}, nowa cena za m2: {new_price_per_m}")
                    elif new_price is False:
                        logging.info(f"Cena oferty {id} ({id_db}) nie zmieniła się.")
                    else: # new_price is None, but price_check_result was not None. This case might indicate an issue in check_if_price_changed logic if it's not (id_db, False, False)
                        logging.warning(f"check_if_price_changed zwrócił nieoczekiwany wynik dla oferty {id} ({id_db}): {price_check_result}")
                else:
                    logging.error(f"Nie udało się sprawdzić zmiany ceny dla oferty {id} (check_if_price_changed zwrócił None).")

        # Na koncu sprawdz, czy sa jakies usuniete oferty
        logging.info("Rozpoczynam sprawdzanie czy czy jakieś oferty nie zostały usunięte z otodom...")
        deleted_offers = find_closed_offers(all_offers_basic_from_sarching_page, city,cur)
        logging.info("Rozpocznynam update ofert w bazie, które zostały usunięte...")
        for deletd_offer in deleted_offers:
            update_deleted_offers(deletd_offer, conn, cur)
        
        logging.info("Zakończono")
            
    except Exception as error:
        logging.exception("Error in main fucntion:")
    finally: 
        if conn:
            if cur: # Ensure cur is not None before closing
                cur.close()
            conn.close()



if __name__ == "__main__": 
    main()
