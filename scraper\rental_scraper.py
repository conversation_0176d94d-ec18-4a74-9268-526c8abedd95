import requests, logging
from urllib.robotparser import RobotFileParser
from scraper.utils import save_data_to_excel

from scraper.rental_fetch_and_parse import fetch_page, download_rental_data_from_search_results, download_rental_data_from_listing_page, find_closed_rental_offers
from scraper.rental_transform_data import transform_rental_data

# Przykładowy URL dla wynajmu mieszkań w Warszawie
url_rental_main = "https://www.otodom.pl/pl/wyniki/wynajem/mieszkanie/mazowieckie/warszawa/warszawa/warszawa?viewType=listing&by=LATEST&direction=DESC&limit=72"


def is_allowed_to_scrape(url: str) -> bool:
    """
    Checks if scraping is allowed for the given URL by consulting the robots.txt file.

    Args:
        url (str): The URL to check for scraping permissions

    Returns:
        bool: True if scraping is allowed, False otherwise
    """
    try:
        rp = RobotFileParser()
        rp.set_url("https://www.otodom.pl/robots.txt")
        rp.read()
        return rp.can_fetch("*", url)
    except Exception as error:
        logging.warning(f"Nie można sprawdzić robots.txt dla {url}: {error}")
        return True  # Domyślnie pozwalamy na scraping jeśli nie można sprawdzić


def scrape_rental_offer(offer: dict) -> dict | None:
    """
    Scrapes detailed rental offer data from a single listing page.

    Args:
        offer (dict): Dictionary containing basic offer data with 'link' key

    Returns:
        dict | None: Transformed rental offer data or None if scraping fails
    """
    try:
        link_offer = offer.get("link")
        if not link_offer:
            logging.error("Brak linku w ofercie wynajmu")
            return None

        logging.debug(f"Pobieranie szczegółów oferty wynajmu: {link_offer}")
        
        response = fetch_page(link_offer)
        if response is None:
            logging.error(f"Nie udało się pobrać strony oferty wynajmu: {link_offer}")
            return None

        offer_data = download_rental_data_from_listing_page(response)
        if offer_data is None:
            logging.error(f"Nie udało się wyodrębnić danych z oferty wynajmu: {link_offer}")
            return None

        cleaned_offer_data = transform_rental_data(offer_data)
        if cleaned_offer_data is None:
            logging.error(f"Nie udało się przetworzyć danych oferty wynajmu: {link_offer}")
            return None

        logging.debug(f"Pomyślnie pobrano i przetworzono ofertę wynajmu: {link_offer}")
        return cleaned_offer_data

    except Exception as error:
        logging.exception(f"Błąd podczas pobierania oferty wynajmu {offer.get('link', 'UNKNOWN')}: {error}")
        return None


def scrape_all_rental_pages_to_excel(url=url_rental_main):
    """
    Scrapes all rental pages and saves data to Excel file.
    Not in use, left just in case, not updated for current workflow.
    """
    response = fetch_page(url)
    offers = download_rental_data_from_search_results(response)
    
    for offer in offers:
        link_offer = offer.get("link")
        response = fetch_page(link_offer)
        offer_data = download_rental_data_from_listing_page(response)
        cleaned_offer_data = transform_rental_data(offer_data)
        cleaned_offer_data.pop('images', None)  # Remove images for Excel export
        save_data_to_excel(cleaned_offer_data, 'output_data/rental_data_warszawa.xlsx')


def validate_rental_offer_data(offer_data: dict) -> bool:
    """
    Validates that rental offer data contains required fields.

    Args:
        offer_data (dict): Rental offer data to validate

    Returns:
        bool: True if data is valid, False otherwise
    """
    required_fields = [
        'listing_id', 'title', 'rental_price', 'area', 
        'voivodeship', 'city', 'offer_link'
    ]
    
    for field in required_fields:
        if field not in offer_data or offer_data[field] is None:
            logging.warning(f"Brak wymaganego pola '{field}' w danych oferty wynajmu")
            return False
    
    # Sprawdź czy cena wynajmu jest liczbą dodatnią
    if not isinstance(offer_data['rental_price'], (int, float)) or offer_data['rental_price'] <= 0:
        logging.warning(f"Nieprawidłowa cena wynajmu: {offer_data['rental_price']}")
        return False
    
    # Sprawdź czy powierzchnia jest liczbą dodatnią
    if not isinstance(offer_data['area'], (int, float)) or offer_data['area'] <= 0:
        logging.warning(f"Nieprawidłowa powierzchnia: {offer_data['area']}")
        return False
    
    return True


def get_rental_search_url(city: str, voivodeship: str | None = None, district: str | None = None,
                         sort_by: str = "LATEST", direction: str = "DESC",
                         limit: int = 72) -> str:
    """
    Constructs a rental search URL for Otodom.pl based on location parameters.

    Args:
        city (str): City name (required)
        voivodeship (str, optional): Voivodeship name
        district (str, optional): District name
        sort_by (str): Sorting method (default: "LATEST")
        direction (str): Sort direction (default: "DESC")
        limit (int): Number of results per page (default: 72)

    Returns:
        str: Constructed search URL for rental apartments
    """
    base_url = "https://www.otodom.pl/pl/wyniki/wynajem/mieszkanie"
    
    # Buduj URL na podstawie lokalizacji
    url_parts = [base_url]
    
    if voivodeship:
        url_parts.append(voivodeship.lower())
    if city:
        url_parts.append(city.lower())
    if district:
        url_parts.append(district.lower())
    
    url = "/".join(url_parts)
    
    # Dodaj parametry wyszukiwania
    params = f"?viewType=listing&by={sort_by}&direction={direction}&limit={limit}"
    
    return url + params


def log_rental_scraping_summary(total_offers: int, new_offers: int, 
                               updated_offers: int, closed_offers: int):
    """
    Logs a summary of the rental scraping session.

    Args:
        total_offers (int): Total number of offers processed
        new_offers (int): Number of new offers added
        updated_offers (int): Number of offers with price updates
        closed_offers (int): Number of offers marked as closed
    """
    logging.info("=" * 60)
    logging.info("PODSUMOWANIE SCRAPINGU OFERT WYNAJMU")
    logging.info("=" * 60)
    logging.info(f"Łączna liczba przetworzonych ofert: {total_offers}")
    logging.info(f"Nowe oferty dodane do bazy: {new_offers}")
    logging.info(f"Oferty z zaktualizowaną ceną: {updated_offers}")
    logging.info(f"Oferty oznaczone jako zamknięte: {closed_offers}")
    logging.info("=" * 60)


# Przykład użycia
if __name__ == "__main__":
    # Test podstawowych funkcji
    test_url = get_rental_search_url("warszawa", "mazowieckie")
    print(f"Test URL dla wynajmu w Warszawie: {test_url}")
    
    # Test sprawdzania robots.txt
    allowed = is_allowed_to_scrape(test_url)
    print(f"Czy scraping jest dozwolony: {allowed}")
