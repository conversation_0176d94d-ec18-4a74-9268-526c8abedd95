#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to manually create rental tables in the database.
Run this if the automatic table creation fails.
"""

import os
import sys
import logging
from dotenv import load_dotenv
import psycopg2

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_db_connection():
    """Get database connection"""
    try:
        connection = psycopg2.connect(
            host=os.getenv('DB_HOST'),
            dbname=os.getenv('DB_NAME'),
            user=os.getenv('DB_USER'),
            password=os.getenv('DB_PASSWORD'),
            port=os.getenv('DB_PORT')
        )
        logging.info(f"Connected to database {os.getenv('DB_NAME')}")
        return connection
    except Exception as error:
        logging.error(f"Error connecting to database: {error}")
        return None

def check_table_exists(cur, table_name):
    """Check if table exists"""
    query = """
        SELECT EXISTS (
            SELECT 1
            FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_name = %s
        );
    """
    cur.execute(query, (table_name,))
    result = cur.fetchone()
    return result[0]

def create_rental_tables():
    """Create rental tables manually"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cur = conn.cursor()
        
        # Check which rental tables exist
        rental_tables = [
            'apartments_rental_listings',
            'rental_price_history', 
            'rental_photos',
            'rental_features'
        ]
        
        existing_tables = []
        missing_tables = []
        
        for table in rental_tables:
            if check_table_exists(cur, table):
                existing_tables.append(table)
            else:
                missing_tables.append(table)
        
        if existing_tables:
            logging.info(f"Existing rental tables: {existing_tables}")
        
        if not missing_tables:
            logging.info("All rental tables already exist!")
            return True
        
        logging.info(f"Creating missing rental tables: {missing_tables}")
        
        # Create apartments_rental_listings table
        if 'apartments_rental_listings' in missing_tables:
            logging.info("Creating apartments_rental_listings table...")
            cur.execute("""
                CREATE TABLE apartments_rental_listings (
                    id SERIAL PRIMARY KEY,
                    otodom_listing_id BIGINT,
                    title TEXT,
                    market TEXT,
                    advert_type TEXT,
                    creation_date DATE,
                    creation_time TEXT,
                    pushed_ap_at DATE,
                    exclusive_offer BOOLEAN,
                    creation_source TEXT,
                    description_text TEXT,
                    area NUMERIC(10, 2),
                    rental_price BIGINT,
                    updated_rental_price BIGINT,
                    rental_price_per_m NUMERIC(10, 2),
                    updated_rental_price_per_m NUMERIC(10,2),
                    deposit_amount BIGINT,
                    utilities_included BOOLEAN,
                    location_id BIGINT,
                    street TEXT,
                    additional_rent BIGINT,
                    rooms_num INT,
                    floor_num VARCHAR(5),
                    heating TEXT,
                    ownership TEXT,
                    proper_type TEXT,
                    construction_status TEXT,
                    energy_certificate TEXT,
                    building_build_year INT,
                    building_floors_num INT,
                    building_material TEXT,
                    building_type TEXT,
                    windows_type TEXT,
                    local_plan_url TEXT,
                    video_url TEXT,
                    view3d_url TEXT,
                    walkaround_url TEXT,
                    owner_id BIGINT,
                    owner_name TEXT,
                    agency_id BIGINT,
                    agency_name TEXT,
                    offer_link TEXT,
                    active BOOLEAN,
                    closing_date DATE,
                    FOREIGN KEY(location_id) REFERENCES locations(id)
                );
            """)
            logging.info("✓ apartments_rental_listings table created")
        
        # Create rental_price_history table
        if 'rental_price_history' in missing_tables:
            logging.info("Creating rental_price_history table...")
            cur.execute("""
                CREATE TABLE rental_price_history (
                    id SERIAL PRIMARY KEY,
                    listing_id BIGINT,
                    old_price INT,
                    new_price INT,
                    change_date DATE,
                    FOREIGN KEY (listing_id) REFERENCES apartments_rental_listings(id)
                );
            """)
            logging.info("✓ rental_price_history table created")
        
        # Create rental_photos table
        if 'rental_photos' in missing_tables:
            logging.info("Creating rental_photos table...")
            cur.execute("""
                CREATE TABLE rental_photos (
                    id SERIAL PRIMARY KEY,
                    listing_id BIGINT,
                    photo BYTEA,
                    FOREIGN KEY (listing_id) REFERENCES apartments_rental_listings(id)
                );
            """)
            logging.info("✓ rental_photos table created")
        
        # Create rental_features table
        if 'rental_features' in missing_tables:
            logging.info("Creating rental_features table...")
            cur.execute("""
                CREATE TABLE rental_features (
                    listing_id BIGINT PRIMARY KEY,
                    internet BOOLEAN,
                    cable_television BOOLEAN,
                    phone BOOLEAN,
                    roller_shutters BOOLEAN,
                    anti_burglary_door BOOLEAN,
                    entryphone BOOLEAN,
                    monitoring BOOLEAN,
                    alarm BOOLEAN,
                    closed_area BOOLEAN,
                    furniture BOOLEAN,
                    washing_machine BOOLEAN,
                    dishwasher BOOLEAN,
                    fridge BOOLEAN,
                    stove BOOLEAN,
                    oven BOOLEAN,
                    tv BOOLEAN,
                    balcony BOOLEAN,
                    usable_room BOOLEAN,
                    garage BOOLEAN,
                    basement BOOLEAN,
                    garden BOOLEAN,
                    terrace BOOLEAN,
                    lift BOOLEAN,
                    two_storey BOOLEAN,
                    separate_kitchen BOOLEAN,
                    air_conditioning BOOLEAN,
                    FOREIGN KEY (listing_id) REFERENCES apartments_rental_listings(id)
                );
            """)
            logging.info("✓ rental_features table created")
        
        # Commit changes
        conn.commit()
        logging.info("✓ All rental tables created successfully!")
        
        return True
        
    except Exception as error:
        logging.exception(f"Error creating rental tables: {error}")
        conn.rollback()
        return False
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()

def main():
    """Main function"""
    logging.info("Starting rental tables creation...")
    
    success = create_rental_tables()
    
    if success:
        logging.info("✓ Rental tables setup completed successfully!")
        print("\n" + "="*60)
        print("SUCCESS: Rental tables are ready!")
        print("You can now run: python main_rental.py")
        print("="*60)
    else:
        logging.error("✗ Failed to create rental tables")
        print("\n" + "="*60)
        print("ERROR: Failed to create rental tables")
        print("Check the logs above for details")
        print("="*60)
        sys.exit(1)

if __name__ == "__main__":
    main()
