#!/usr/bin/env python3
"""
Test script for the rental apartment scraper.
This script validates the rental scraping functionality without affecting the main database.
"""

import os
import sys
import logging
from datetime import datetime

# Add scraper path
scraper_path = os.path.join(os.path.dirname(__file__), 'scraper')
if scraper_path not in sys.path:
    sys.path.append(scraper_path)

from scraper.rental_scraper import (
    is_allowed_to_scrape, 
    get_rental_search_url, 
    validate_rental_offer_data
)
from scraper.rental_fetch_and_parse import (
    fetch_page, 
    download_rental_data_from_search_results,
    download_rental_data_from_listing_page
)
from scraper.rental_transform_data import (
    transform_rental_data,
    validate_transformed_rental_data
)

# Setup basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_url_generation():
    """Test rental search URL generation"""
    print("\n" + "="*60)
    print("TESTING URL GENERATION")
    print("="*60)
    
    # Test different city configurations
    test_cases = [
        ("warszawa", "mazowieckie", None),
        ("kraków", "małopolskie", None),
        ("gdańsk", "pomorskie", None),
        ("katowice", "śląskie", None),
    ]
    
    for city, voivodeship, district in test_cases:
        url = get_rental_search_url(city, voivodeship, district)
        print(f"City: {city.title()}, Voivodeship: {voivodeship}")
        print(f"URL: {url}")
        print("-" * 40)
    
    return True


def test_robots_txt():
    """Test robots.txt compliance"""
    print("\n" + "="*60)
    print("TESTING ROBOTS.TXT COMPLIANCE")
    print("="*60)
    
    test_url = get_rental_search_url("warszawa", "mazowieckie")
    allowed = is_allowed_to_scrape(test_url)
    
    print(f"Test URL: {test_url}")
    print(f"Scraping allowed: {allowed}")
    
    return allowed


def test_basic_page_fetch():
    """Test basic page fetching functionality"""
    print("\n" + "="*60)
    print("TESTING BASIC PAGE FETCH")
    print("="*60)
    
    test_url = get_rental_search_url("warszawa", "mazowieckie")
    print(f"Fetching: {test_url}")
    
    try:
        response = fetch_page(test_url)
        if response:
            print(f"✓ Successfully fetched page (Status: {response.status_code})")
            print(f"✓ Content length: {len(response.text)} characters")
            return True
        else:
            print("✗ Failed to fetch page")
            return False
    except Exception as e:
        print(f"✗ Error fetching page: {e}")
        return False


def test_search_results_parsing():
    """Test parsing of search results"""
    print("\n" + "="*60)
    print("TESTING SEARCH RESULTS PARSING")
    print("="*60)
    
    test_url = get_rental_search_url("warszawa", "mazowieckie")
    print(f"Parsing search results from: {test_url}")
    
    try:
        offers = download_rental_data_from_search_results(test_url)
        
        if offers:
            print(f"✓ Successfully parsed {len(offers)} rental offers")
            
            # Show first few offers
            for i, offer in enumerate(offers[:3]):
                print(f"\nOffer {i+1}:")
                print(f"  ID: {offer.get('listing_id')}")
                print(f"  Area: {offer.get('area')} m²")
                print(f"  Rental Price: {offer.get('rental_price')} PLN")
                print(f"  Price per m²: {offer.get('rental_price_per_m')} PLN/m²")
                print(f"  Link: {offer.get('link')}")
            
            return offers[:2]  # Return first 2 offers for detailed testing
        else:
            print("✗ No rental offers found")
            return []
            
    except Exception as e:
        print(f"✗ Error parsing search results: {e}")
        logging.exception("Detailed error:")
        return []


def test_detailed_offer_parsing(offers):
    """Test parsing of detailed offer pages"""
    print("\n" + "="*60)
    print("TESTING DETAILED OFFER PARSING")
    print("="*60)
    
    if not offers:
        print("No offers to test detailed parsing")
        return []
    
    detailed_offers = []
    
    for i, offer in enumerate(offers):
        print(f"\nTesting offer {i+1}: {offer.get('listing_id')}")
        
        try:
            link = offer.get('link')
            if not link:
                print("✗ No link available")
                continue
                
            response = fetch_page(link)
            if not response:
                print("✗ Failed to fetch offer page")
                continue
                
            detailed_data = download_rental_data_from_listing_page(response)
            if detailed_data:
                print(f"✓ Successfully parsed detailed data")
                print(f"  Title: {detailed_data.get('title', 'N/A')[:50]}...")
                print(f"  Rental Price: {detailed_data.get('rental_price')} PLN")
                print(f"  Deposit: {detailed_data.get('deposit_amount')} PLN")
                print(f"  Utilities Included: {detailed_data.get('utilities_included')}")
                print(f"  Rooms: {detailed_data.get('rooms_num')}")
                print(f"  Floor: {detailed_data.get('floor_num')}")
                
                detailed_offers.append(detailed_data)
            else:
                print("✗ Failed to parse detailed data")
                
        except Exception as e:
            print(f"✗ Error parsing detailed offer: {e}")
            logging.exception("Detailed error:")
    
    return detailed_offers


def test_data_transformation(detailed_offers):
    """Test data transformation functionality"""
    print("\n" + "="*60)
    print("TESTING DATA TRANSFORMATION")
    print("="*60)
    
    if not detailed_offers:
        print("No detailed offers to test transformation")
        return []
    
    transformed_offers = []
    
    for i, offer in enumerate(detailed_offers):
        print(f"\nTransforming offer {i+1}")
        
        try:
            transformed = transform_rental_data(offer)
            if transformed:
                print("✓ Successfully transformed data")
                
                # Validate transformed data
                is_valid = validate_transformed_rental_data(transformed)
                print(f"✓ Data validation: {'PASSED' if is_valid else 'FAILED'}")
                
                # Show key transformed fields
                print(f"  Listing ID: {transformed.get('listing_id')}")
                print(f"  Rental Price: {transformed.get('rental_price')} PLN")
                print(f"  Area: {transformed.get('area')} m²")
                print(f"  Rooms: {transformed.get('rooms_num')}")
                print(f"  Creation Date: {transformed.get('creation_date')}")
                
                transformed_offers.append(transformed)
            else:
                print("✗ Failed to transform data")
                
        except Exception as e:
            print(f"✗ Error transforming data: {e}")
            logging.exception("Detailed error:")
    
    return transformed_offers


def run_full_test():
    """Run complete test suite"""
    print("RENTAL APARTMENT SCRAPER TEST SUITE")
    print("=" * 80)
    print(f"Test started at: {datetime.now()}")
    
    # Test 1: URL Generation
    url_test = test_url_generation()
    
    # Test 2: Robots.txt
    robots_test = test_robots_txt()
    
    # Test 3: Basic page fetch
    fetch_test = test_basic_page_fetch()
    
    # Test 4: Search results parsing
    offers = test_search_results_parsing()
    
    # Test 5: Detailed offer parsing
    detailed_offers = test_detailed_offer_parsing(offers)
    
    # Test 6: Data transformation
    transformed_offers = test_data_transformation(detailed_offers)
    
    # Summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    print(f"URL Generation: {'✓ PASSED' if url_test else '✗ FAILED'}")
    print(f"Robots.txt Check: {'✓ PASSED' if robots_test else '✗ FAILED'}")
    print(f"Page Fetch: {'✓ PASSED' if fetch_test else '✗ FAILED'}")
    print(f"Search Results: {'✓ PASSED' if offers else '✗ FAILED'} ({len(offers)} offers)")
    print(f"Detailed Parsing: {'✓ PASSED' if detailed_offers else '✗ FAILED'} ({len(detailed_offers)} detailed)")
    print(f"Data Transformation: {'✓ PASSED' if transformed_offers else '✗ FAILED'} ({len(transformed_offers)} transformed)")
    
    overall_success = all([url_test, robots_test, fetch_test, bool(offers), bool(detailed_offers), bool(transformed_offers)])
    print(f"\nOVERALL: {'✓ ALL TESTS PASSED' if overall_success else '✗ SOME TESTS FAILED'}")
    print(f"Test completed at: {datetime.now()}")
    
    return overall_success


if __name__ == "__main__":
    success = run_full_test()
    sys.exit(0 if success else 1)
