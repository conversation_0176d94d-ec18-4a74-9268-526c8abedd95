#!/usr/bin/env python3
"""
Simple script to create rental database tables.
This script creates only the rental-specific tables without touching existing ones.
"""

import os
import sys
import logging
from dotenv import load_dotenv
import psycopg2

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_db_connection():
    """Get database connection"""
    try:
        connection = psycopg2.connect(
            host=os.getenv('DB_HOST'),
            dbname=os.getenv('DB_NAME'),
            user=os.getenv('DB_USER'),
            password=os.getenv('DB_PASSWORD'),
            port=os.getenv('DB_PORT')
        )
        logging.info(f"Connected to database {os.getenv('DB_NAME')}")
        return connection
    except Exception as error:
        logging.error(f"Error connecting to database: {error}")
        return None

def create_rental_tables():
    """Create rental tables"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cur = conn.cursor()
        
        logging.info("Creating rental database tables...")
        
        # 1. Create apartments_rental_listings table
        logging.info("Creating apartments_rental_listings table...")
        cur.execute("""
            CREATE TABLE IF NOT EXISTS apartments_rental_listings (
                id SERIAL PRIMARY KEY,
                otodom_listing_id BIGINT,
                title TEXT,
                market TEXT,
                advert_type TEXT,
                creation_date DATE,
                creation_time TEXT,
                pushed_ap_at DATE,
                exclusive_offer BOOLEAN,
                creation_source TEXT,
                description_text TEXT,
                area NUMERIC(10, 2),
                rental_price BIGINT,
                updated_rental_price BIGINT,
                rental_price_per_m NUMERIC(10, 2),
                updated_rental_price_per_m NUMERIC(10,2),
                deposit_amount BIGINT,
                utilities_included BOOLEAN,
                location_id BIGINT,
                street TEXT,
                additional_rent BIGINT,
                rooms_num INT,
                floor_num VARCHAR(5),
                heating TEXT,
                ownership TEXT,
                proper_type TEXT,
                construction_status TEXT,
                energy_certificate TEXT,
                building_build_year INT,
                building_floors_num INT,
                building_material TEXT,
                building_type TEXT,
                windows_type TEXT,
                local_plan_url TEXT,
                video_url TEXT,
                view3d_url TEXT,
                walkaround_url TEXT,
                owner_id BIGINT,
                owner_name TEXT,
                agency_id BIGINT,
                agency_name TEXT,
                offer_link TEXT,
                active BOOLEAN,
                closing_date DATE,
                FOREIGN KEY(location_id) REFERENCES locations(id)
            );
        """)
        logging.info("✓ apartments_rental_listings table created")
        
        # 2. Create rental_price_history table
        logging.info("Creating rental_price_history table...")
        cur.execute("""
            CREATE TABLE IF NOT EXISTS rental_price_history (
                id SERIAL PRIMARY KEY,
                listing_id BIGINT,
                old_price INT,
                new_price INT,
                change_date DATE,
                FOREIGN KEY (listing_id) REFERENCES apartments_rental_listings(id)
            );
        """)
        logging.info("✓ rental_price_history table created")
        
        # 3. Create rental_photos table
        logging.info("Creating rental_photos table...")
        cur.execute("""
            CREATE TABLE IF NOT EXISTS rental_photos (
                id SERIAL PRIMARY KEY,
                listing_id BIGINT,
                photo BYTEA,
                FOREIGN KEY (listing_id) REFERENCES apartments_rental_listings(id)
            );
        """)
        logging.info("✓ rental_photos table created")
        
        # 4. Create rental_features table
        logging.info("Creating rental_features table...")
        cur.execute("""
            CREATE TABLE IF NOT EXISTS rental_features (
                listing_id BIGINT PRIMARY KEY,
                internet BOOLEAN,
                cable_television BOOLEAN,
                phone BOOLEAN,
                roller_shutters BOOLEAN,
                anti_burglary_door BOOLEAN,
                entryphone BOOLEAN,
                monitoring BOOLEAN,
                alarm BOOLEAN,
                closed_area BOOLEAN,
                furniture BOOLEAN,
                washing_machine BOOLEAN,
                dishwasher BOOLEAN,
                fridge BOOLEAN,
                stove BOOLEAN,
                oven BOOLEAN,
                tv BOOLEAN,
                balcony BOOLEAN,
                usable_room BOOLEAN,
                garage BOOLEAN,
                basement BOOLEAN,
                garden BOOLEAN,
                terrace BOOLEAN,
                lift BOOLEAN,
                two_storey BOOLEAN,
                separate_kitchen BOOLEAN,
                air_conditioning BOOLEAN,
                FOREIGN KEY (listing_id) REFERENCES apartments_rental_listings(id)
            );
        """)
        logging.info("✓ rental_features table created")
        
        # Commit all changes
        conn.commit()
        logging.info("✓ All rental tables created successfully!")
        
        # Verify tables exist
        cur.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name LIKE '%rental%'
            ORDER BY table_name;
        """)
        
        rental_tables = cur.fetchall()
        logging.info(f"Verified rental tables: {[table[0] for table in rental_tables]}")
        
        return True
        
    except Exception as error:
        logging.exception(f"Error creating rental tables: {error}")
        conn.rollback()
        return False
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()

def main():
    """Main function"""
    print("="*60)
    print("RENTAL DATABASE SETUP")
    print("="*60)
    
    logging.info("Starting rental database setup...")
    
    success = create_rental_tables()
    
    if success:
        print("\n✓ SUCCESS: Rental database tables created!")
        print("\nYou can now run:")
        print("  python main_rental.py")
        print("  python test_rental_parsing.py")
        logging.info("Rental database setup completed successfully!")
    else:
        print("\n✗ ERROR: Failed to create rental tables")
        print("Check the logs above for details")
        logging.error("Rental database setup failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
