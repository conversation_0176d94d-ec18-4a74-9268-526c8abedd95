#!/usr/bin/env python3
"""
Test script to validate rental database operations.
"""

import os
import sys
import logging
from datetime import datetime

# Add paths
scraper_path = os.path.join(os.path.dirname(__file__), 'scraper')
if scraper_path not in sys.path:
    sys.path.append(scraper_path)

from db.db_setup import get_db_connection
from db.rental_db_operations import insert_new_rental_listing
from scraper.rental_fetch_and_parse import check_if_rental_offer_exists

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_database_connection():
    """Test database connection"""
    print("\n" + "="*50)
    print("TESTING DATABASE CONNECTION")
    print("="*50)
    
    conn = get_db_connection()
    if conn:
        print("✓ Database connection successful")
        
        # Test table existence
        cur = conn.cursor()
        cur.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name LIKE '%rental%'
            ORDER BY table_name;
        """)
        
        rental_tables = cur.fetchall()
        print(f"✓ Found rental tables: {[table[0] for table in rental_tables]}")
        
        cur.close()
        conn.close()
        return True
    else:
        print("✗ Database connection failed")
        return False

def test_rental_insertion():
    """Test rental data insertion"""
    print("\n" + "="*50)
    print("TESTING RENTAL DATA INSERTION")
    print("="*50)
    
    # Create test data
    test_offer_data = {
        'listing_id': 99999999,  # Test ID
        'title': 'Test Rental Apartment',
        'market': 'secondary',
        'advert_type': 'private',
        'creation_date': datetime.now().date(),
        'creation_time': '12:00',
        'pushed_ap_at': None,
        'exclusive_offer': False,
        'creation_source': 'manual',
        'description_text': 'Test rental apartment description',
        'area': 50.0,
        'rental_price': 3000,
        'rental_price_per_m': 60.0,
        'deposit_amount': 3000,
        'utilities_included': False,
        'additional_rent': 500,
        'voivodeship': 'mazowieckie',
        'city': 'warszawa',
        'district': 'mokotów',
        'street': 'testowa',
        'rooms_num': 2,
        'floor_num': '3',
        'heating': 'central',
        'ownership': 'full_ownership',
        'proper_type': 'mieszkanie',
        'construction_status': 'ready',
        'energy_certificate': 'B',
        'building_build_year': 2010,
        'building_floors_num': 5,
        'building_material': 'brick',
        'building_type': 'apartment_building',
        'windows_type': 'plastic',
        'features': 'internet furniture balcony',
        'local_plan_url': None,
        'video_url': None,
        'view3d_url': None,
        'walkaround_url': None,
        'images': [],
        'owner_id': 12345,
        'owner_name': 'Test Owner',
        'agency_id': None,
        'agency_name': None,
        'offer_link': 'https://test.com/test-offer',
        'active': True,
        'closing_date': None
    }
    
    conn = get_db_connection()
    if not conn:
        print("✗ Cannot connect to database")
        return False
    
    try:
        cur = conn.cursor()
        
        # Check if test offer already exists
        test_offer = {'listing_id': test_offer_data['listing_id']}
        if check_if_rental_offer_exists(test_offer, cur):
            print("Test offer already exists, deleting it first...")
            cur.execute("DELETE FROM rental_features WHERE listing_id IN (SELECT id FROM apartments_rental_listings WHERE otodom_listing_id = %s)", (test_offer_data['listing_id'],))
            cur.execute("DELETE FROM rental_photos WHERE listing_id IN (SELECT id FROM apartments_rental_listings WHERE otodom_listing_id = %s)", (test_offer_data['listing_id'],))
            cur.execute("DELETE FROM apartments_rental_listings WHERE otodom_listing_id = %s", (test_offer_data['listing_id'],))
            conn.commit()
            print("✓ Test offer cleaned up")
        
        # Insert test offer
        print("Inserting test rental offer...")
        result_id = insert_new_rental_listing(test_offer_data, conn, cur)
        
        if result_id:
            print(f"✓ Test rental offer inserted successfully with ID: {result_id}")
            
            # Verify insertion
            cur.execute("SELECT COUNT(*) FROM apartments_rental_listings WHERE id = %s", (result_id,))
            count = cur.fetchone()[0]
            if count == 1:
                print("✓ Rental offer verified in database")
                
                # Clean up test data
                cur.execute("DELETE FROM rental_features WHERE listing_id = %s", (result_id,))
                cur.execute("DELETE FROM rental_photos WHERE listing_id = %s", (result_id,))
                cur.execute("DELETE FROM apartments_rental_listings WHERE id = %s", (result_id,))
                conn.commit()
                print("✓ Test data cleaned up")
                
                return True
            else:
                print("✗ Rental offer not found in database after insertion")
                return False
        else:
            print("✗ Failed to insert test rental offer")
            return False
            
    except Exception as error:
        logging.exception(f"Error during rental insertion test: {error}")
        try:
            conn.rollback()
        except:
            pass
        return False
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()

def main():
    """Main test function"""
    print("RENTAL DATABASE TEST SUITE")
    print("=" * 80)
    
    # Test 1: Database connection
    db_test = test_database_connection()
    
    if not db_test:
        print("\n✗ Database connection failed - cannot continue")
        return False
    
    # Test 2: Rental data insertion
    insertion_test = test_rental_insertion()
    
    # Summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    print(f"Database Connection: {'✓ PASSED' if db_test else '✗ FAILED'}")
    print(f"Rental Data Insertion: {'✓ PASSED' if insertion_test else '✗ FAILED'}")
    
    success = db_test and insertion_test
    print(f"\nOVERALL: {'✓ ALL TESTS PASSED' if success else '✗ SOME TESTS FAILED'}")
    
    if success:
        print("\n✓ Rental database is working correctly!")
        print("You can now run: python main_rental.py")
    else:
        print("\n✗ Some issues found with rental database.")
        print("Check the logs above for details.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
