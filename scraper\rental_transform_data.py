import re
import logging
from datetime import datetime


FLOOR_MAPPING = {
    "['ground_floor']": 0,
    "['floor_1']": 1,
    "['floor_2']": 2,
    "['floor_3']": 3,
    "['floor_4']": 4,
    "['floor_5']": 5,
    "['floor_6']": 6,
    "['floor_7']": 7,
    "['floor_8']": 8,
    "['floor_9']": 9,
    "['floor_10']": 10,
    "['floor_higher_10']": "10+"
}


OWNERSHIP_MAPPING = {
    "pełna własność": "full_ownership",
    "spółdzielcze wł. prawo do lokalu": "cooperative_ownership",
}


def clear_floor_num(data: str) -> str | None:
    """
    Maps the floor number from the given input data to a standard value using FLOOR_MAPPING
    """
    if data is None:
        return None
    return FLOOR_MAPPING.get(data, None)


def simplify_ownership(data: str) -> str | None:
    """
    Simplifies the ownership status of a property using the OWNERSHIP_MAPPING
    """
    if data is None:
        return None
    return OWNERSHIP_MAPPING.get(data, None)


def extract_rooms_num(data: str) -> int | None:
    """
    Extracts the number of rooms from a string. It looks for the first numeric value in the string
    """
    if data is None:
        return None
    match = re.search(r'\d+', str(data))
    return int(match.group()) if match else None


def extract_text(data) -> str | None:
    """
    Extracts and cleans text from the given data. If the data is a list, it joins the list into a string

    Args:
    data: The input data (can be a string or a list of strings)

    Returns:
    str: The cleaned text or None if the input data is None

    If the input data is a list, the function joins the elements into a single string and removes unwanted characters
    """
    if data is None:
        return None
    if isinstance(data, list):
        clean = ' '.join(data).strip("[]'")
    else:
        clean = data.strip("[]',")
    return clean


def clean_text(text: str) -> str | None:
    """
    Cleans text by removing extra whitespace and normalizing line breaks
    """
    if text is None:
        return None

    # Remove extra whitespace and normalize line breaks
    cleaned = re.sub(r'\s+', ' ', text.strip())
    return cleaned


def extract_deposit_amount(data) -> int | None:
    """
    Extracts deposit amount from various data formats.
    Handles both numeric values and text descriptions.
    """
    if data is None:
        return None

    if isinstance(data, (int, float)):
        return int(data)

    if isinstance(data, str):
        # Try to extract numeric value from string
        match = re.search(r'(\d+)', data.replace(' ', '').replace(',', ''))
        if match:
            return int(match.group(1))

    return None


def extract_additional_rent(data) -> int | None:
    """
    Extracts additional rent/administrative fees from various data formats.
    """
    if data is None:
        return None

    if isinstance(data, (int, float)):
        return int(data)

    if isinstance(data, str):
        # Try to extract numeric value from string
        match = re.search(r'(\d+)', data.replace(' ', '').replace(',', ''))
        if match:
            return int(match.group(1))

    return None


def normalize_utilities_included(data) -> bool:
    """
    Normalizes utilities included information to boolean.
    """
    if data is None:
        return False
    
    if isinstance(data, bool):
        return data
    
    if isinstance(data, str):
        data_lower = data.lower()
        if any(word in data_lower for word in ['tak', 'yes', 'wliczone', 'included', 'true']):
            return True
        elif any(word in data_lower for word in ['nie', 'no', 'dodatkowo', 'extra', 'false']):
            return False
    
    return False


def combine_rental_features(features_utilities, features_equipment, features_additional_information, security_types):
    """
    Combines all rental features into a single string for database storage.
    """
    all_features = []
    
    if features_utilities:
        all_features.extend(features_utilities if isinstance(features_utilities, list) else [features_utilities])
    if features_equipment:
        all_features.extend(features_equipment if isinstance(features_equipment, list) else [features_equipment])
    if features_additional_information:
        all_features.extend(features_additional_information if isinstance(features_additional_information, list) else [features_additional_information])
    if security_types:
        all_features.extend(security_types if isinstance(security_types, list) else [security_types])
    
    return ' '.join(all_features) if all_features else ''


def transform_rental_data(data: dict) -> dict:
    """
    Transforms the input rental data by cleaning, simplifying, and formatting it to a standardized structure

    Args:
    data (dict): The data dictionary containing information about a rental property listing.

    Returns:
    dict: The transformed data dictionary with cleaned and simplified values.

    This function processes various fields in the input data, such as:
    - Extracting the number of rooms,
    - Mapping the floor number and ownership status to simplified values,
    - Cleaning text fields,
    - Combining features into one field,
    - Formatting rental price, area, and date values,
    - Processing rental-specific fields like deposit and utilities
    """
    if data is None:
        return None
    
    transformed_data = data.copy()

    # Transform basic fields
    transformed_data["rooms_num"] = extract_rooms_num(transformed_data.get("rooms_num"))
    transformed_data["floor_num"] = clear_floor_num(str(transformed_data.get("floor_num")))
    transformed_data["ownership"] = simplify_ownership(transformed_data.get("ownership"))

    # Transform rental-specific fields
    transformed_data["deposit_amount"] = extract_deposit_amount(transformed_data.get("deposit_amount"))
    transformed_data["additional_rent"] = extract_additional_rent(transformed_data.get("additional_rent"))
    transformed_data["utilities_included"] = normalize_utilities_included(transformed_data.get("utilities_included"))

    # Clean text fields
    transformed_data['heating'] = extract_text(transformed_data.get('heating'))
    transformed_data['construction_status'] = extract_text(transformed_data.get('construction_status'))
    transformed_data['building_material'] = extract_text(transformed_data.get('building_material'))
    transformed_data['building_type'] = extract_text(transformed_data.get('building_type'))
    transformed_data['windows_type'] = extract_text(transformed_data.get('windows_type'))
    transformed_data['energy_certificate'] = extract_text(transformed_data.get('energy_certificate'))
    transformed_data['description_text'] = clean_text(transformed_data.get('description_text'))

    # Process creation date and time
    if transformed_data.get('creation_date'):
        try:
            creation_date = datetime.strptime(transformed_data['creation_date'], '%Y-%m-%dT%H:%M:%S%z')
            transformed_data['creation_time'] = creation_date.strftime('%H:%M')
            transformed_data['creation_date'] = creation_date.date()
        except (ValueError, TypeError) as e:
            logging.warning(f"Nie można przetworzyć daty utworzenia: {transformed_data['creation_date']}, błąd: {e}")
            transformed_data['creation_time'] = None
            transformed_data['creation_date'] = None

    # Process pushed_ap_at date
    if transformed_data.get('pushed_ap_at'):
        try:
            pushed_date = datetime.strptime(transformed_data['pushed_ap_at'], '%Y-%m-%dT%H:%M:%S%z')
            transformed_data['pushed_ap_at'] = pushed_date.date()
        except (ValueError, TypeError) as e:
            logging.warning(f"Nie można przetworzyć daty wypchania: {transformed_data['pushed_ap_at']}, błąd: {e}")
            transformed_data['pushed_ap_at'] = None

    # Ensure numeric fields are properly typed
    if transformed_data.get('rental_price'):
        try:
            transformed_data['rental_price'] = int(float(transformed_data['rental_price']))
        except (ValueError, TypeError):
            transformed_data['rental_price'] = None

    if transformed_data.get('rental_price_per_m'):
        try:
            transformed_data['rental_price_per_m'] = float(transformed_data['rental_price_per_m'])
        except (ValueError, TypeError):
            transformed_data['rental_price_per_m'] = None

    if transformed_data.get('area'):
        try:
            transformed_data['area'] = float(transformed_data['area'])
        except (ValueError, TypeError):
            transformed_data['area'] = None

    # Ensure boolean fields are properly typed
    transformed_data['exclusive_offer'] = bool(transformed_data.get('exclusive_offer', False))
    transformed_data['active'] = bool(transformed_data.get('active', True))

    # Set default values for missing fields
    if 'closing_date' not in transformed_data:
        transformed_data['closing_date'] = None

    return transformed_data


def validate_transformed_rental_data(data: dict) -> bool:
    """
    Validates that transformed rental data meets basic requirements.
    
    Args:
        data (dict): Transformed rental data to validate
        
    Returns:
        bool: True if data is valid, False otherwise
    """
    if not data:
        return False
    
    # Check required fields
    required_fields = ['listing_id', 'rental_price', 'area']
    for field in required_fields:
        if field not in data or data[field] is None:
            return False
    
    # Check data types and ranges
    if not isinstance(data['rental_price'], (int, float)) or data['rental_price'] <= 0:
        return False
    
    if not isinstance(data['area'], (int, float)) or data['area'] <= 0:
        return False
    
    return True
