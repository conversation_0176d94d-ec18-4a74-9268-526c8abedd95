#!/usr/bin/env python3
"""
Simple script to create rental tables manually.
"""

import os
import psycopg2
from dotenv import load_dotenv

load_dotenv()

def create_tables():
    try:
        # Connect to database
        conn = psycopg2.connect(
            host=os.getenv('DB_HOST'),
            dbname=os.getenv('DB_NAME'),
            user=os.getenv('DB_USER'),
            password=os.getenv('DB_PASSWORD'),
            port=os.getenv('DB_PORT')
        )
        
        cur = conn.cursor()
        
        print("Creating rental tables...")

        # First, create locations table if it doesn't exist
        cur.execute("""
            CREATE TABLE IF NOT EXISTS locations (
                id SERIAL PRIMARY KEY,
                voivodeship TEXT,
                city TEXT,
                district TEXT
            );
        """)
        print("✓ locations table created/verified")

        # Create apartments_rental_listings table
        cur.execute("""
            CREATE TABLE IF NOT EXISTS apartments_rental_listings (
                id SERIAL PRIMARY KEY,
                otodom_listing_id BIGINT,
                title TEXT,
                market TEXT,
                advert_type TEXT,
                creation_date DATE,
                creation_time TEXT,
                pushed_ap_at DATE,
                exclusive_offer BOOLEAN,
                creation_source TEXT,
                description_text TEXT,
                area NUMERIC(10, 2),
                rental_price BIGINT,
                updated_rental_price BIGINT,
                rental_price_per_m NUMERIC(10, 2),
                updated_rental_price_per_m NUMERIC(10,2),
                deposit_amount BIGINT,
                utilities_included BOOLEAN,
                location_id BIGINT,
                street TEXT,
                additional_rent BIGINT,
                rooms_num INT,
                floor_num VARCHAR(5),
                heating TEXT,
                ownership TEXT,
                proper_type TEXT,
                construction_status TEXT,
                energy_certificate TEXT,
                building_build_year INT,
                building_floors_num INT,
                building_material TEXT,
                building_type TEXT,
                windows_type TEXT,
                local_plan_url TEXT,
                video_url TEXT,
                view3d_url TEXT,
                walkaround_url TEXT,
                owner_id BIGINT,
                owner_name TEXT,
                agency_id BIGINT,
                agency_name TEXT,
                offer_link TEXT,
                active BOOLEAN,
                closing_date DATE,
                FOREIGN KEY(location_id) REFERENCES locations(id)
            );
        """)
        print("✓ apartments_rental_listings created")
        
        # Create rental_price_history table
        cur.execute("""
            CREATE TABLE IF NOT EXISTS rental_price_history (
                id SERIAL PRIMARY KEY,
                listing_id BIGINT,
                old_price INT,
                new_price INT,
                change_date DATE,
                FOREIGN KEY (listing_id) REFERENCES apartments_rental_listings(id)
            );
        """)
        print("✓ rental_price_history created")
        
        # Create rental_photos table
        cur.execute("""
            CREATE TABLE IF NOT EXISTS rental_photos (
                id SERIAL PRIMARY KEY,
                listing_id BIGINT,
                photo BYTEA,
                FOREIGN KEY (listing_id) REFERENCES apartments_rental_listings(id)
            );
        """)
        print("✓ rental_photos created")
        
        # Create rental_features table
        cur.execute("""
            CREATE TABLE IF NOT EXISTS rental_features (
                listing_id BIGINT PRIMARY KEY,
                internet BOOLEAN,
                cable_television BOOLEAN,
                phone BOOLEAN,
                roller_shutters BOOLEAN,
                anti_burglary_door BOOLEAN,
                entryphone BOOLEAN,
                monitoring BOOLEAN,
                alarm BOOLEAN,
                closed_area BOOLEAN,
                furniture BOOLEAN,
                washing_machine BOOLEAN,
                dishwasher BOOLEAN,
                fridge BOOLEAN,
                stove BOOLEAN,
                oven BOOLEAN,
                tv BOOLEAN,
                balcony BOOLEAN,
                usable_room BOOLEAN,
                garage BOOLEAN,
                basement BOOLEAN,
                garden BOOLEAN,
                terrace BOOLEAN,
                lift BOOLEAN,
                two_storey BOOLEAN,
                separate_kitchen BOOLEAN,
                air_conditioning BOOLEAN,
                FOREIGN KEY (listing_id) REFERENCES apartments_rental_listings(id)
            );
        """)
        print("✓ rental_features created")
        
        conn.commit()
        print("\n✓ ALL RENTAL TABLES CREATED SUCCESSFULLY!")
        
        # Verify tables
        cur.execute("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_name LIKE '%rental%'
            ORDER BY table_name;
        """)
        tables = cur.fetchall()
        print(f"Verified tables: {[t[0] for t in tables]}")
        
        cur.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    if create_tables():
        print("\nSUCCESS! You can now run: python main_rental.py")
    else:
        print("\nFAILED! Check your database connection.")
